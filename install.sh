#!/bin/bash
# install.sh: Set up Open WebUI with InfoBlox MCP and LLMs using Docker Compose on macOS/Linux
# Usage: ./install.sh

set -e  # Stop on error

# Default values
PROJECT_DIR="$HOME/infoblox-open-webui"
DEFAULT_OPEN_WEBUI_PORT=3000
DEFAULT_MCP_PORT=8000
SWAGGER_URL="https://raw.githubusercontent.com/infobloxopen/infoblox-swagger-wapi/master/swagger.json"
DEFAULT_GRID_MASTER_URL="https://grid-master/wapi/v2.13.1"
DEFAULT_MCP_SERVER_URL="http://localhost"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a port is free and find an alternative
check_port() {
    local port=$1
    local max_attempts=10
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if ! lsof -i :$port >/dev/null 2>&1; then
            echo $port
            return
        fi
        port=$((port + 1))
        attempt=$((attempt + 1))
    done
    
    print_error "Could not find a free port after $max_attempts attempts"
    exit 1
}

# Function to prompt for input with default value
prompt_with_default() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    echo -n "$prompt (default: $default): "
    read input
    if [ -z "$input" ]; then
        eval "$var_name=\"$default\""
    else
        eval "$var_name=\"$input\""
    fi
}

# Function to prompt for password
prompt_password() {
    local prompt="$1"
    local default="$2"
    local var_name="$3"
    
    echo -n "$prompt (default: $default): "
    read -s input
    echo
    if [ -z "$input" ]; then
        eval "$var_name=\"$default\""
    else
        eval "$var_name=\"$input\""
    fi
}

print_info "Starting Open WebUI with InfoBlox MCP setup for macOS/Linux..."

# Check for required tools
print_info "Checking for required tools..."

if ! command -v docker &> /dev/null; then
    print_error "Docker is required. Please install Docker Desktop and try again."
    print_info "Download from: https://www.docker.com/products/docker-desktop"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is required. Please install Docker Compose and try again."
    exit 1
fi

print_success "Docker and Docker Compose are available"

# Prompt for user inputs
print_info "Please provide the following configuration details:"

prompt_with_default "Enter InfoBlox Grid Master URL" "$DEFAULT_GRID_MASTER_URL" "GRID_MASTER_URL"
prompt_with_default "Enter InfoBlox username" "admin" "INFOBLOX_USERNAME"
prompt_password "Enter InfoBlox password" "infoblox" "INFOBLOX_PASSWORD"
prompt_password "Enter xAI API key for Grok (leave blank if not using)" "" "XAI_API_KEY"
prompt_password "Enter Anthropic API key for Claude (leave blank if not using)" "" "ANTHROPIC_API_KEY"

# Check and assign ports
OPEN_WEBUI_PORT=$(check_port $DEFAULT_OPEN_WEBUI_PORT)
MCP_PORT=$(check_port $DEFAULT_MCP_PORT)
MCP_SERVER_URL="$DEFAULT_MCP_SERVER_URL:$MCP_PORT"

print_info "Using Open WebUI port: $OPEN_WEBUI_PORT"
print_info "Using MCP port: $MCP_PORT"

# Choose Docker setup
echo
print_info "Choose Docker setup:"
echo "1) Single container (Open WebUI + MCP)"
echo "2) Two containers (Open WebUI, MCP separate)"
echo -n "Enter 1 or 2: "
read DOCKER_CHOICE

if [[ "$DOCKER_CHOICE" != "1" && "$DOCKER_CHOICE" != "2" ]]; then
    print_error "Invalid choice. Exiting."
    exit 1
fi

# Create project directory
print_info "Creating project directory at $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Create directories for Open WebUI and MCP
mkdir -p "open-webui/config"
mkdir -p "open-webui/pipelines"

# Download Swagger JSON
print_info "Downloading Swagger JSON from $SWAGGER_URL"
if command -v curl &> /dev/null; then
    curl -L "$SWAGGER_URL" -o "open-webui/swagger.json"
elif command -v wget &> /dev/null; then
    wget "$SWAGGER_URL" -O "open-webui/swagger.json"
else
    print_error "Neither curl nor wget is available. Please install one of them."
    exit 1
fi

cp "open-webui/swagger.json" "swagger.json"
print_success "Swagger JSON downloaded successfully"

# Create MCP server script
print_info "Creating MCP server script..."
cat > mcp_server.py << 'EOF'
import requests
import yaml
import json
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import os

app = FastAPI()

# Configuration
GRID_MASTER_URL = os.getenv("GRID_MASTER_URL")
INFOBLOX_USERNAME = os.getenv("INFOBLOX_USERNAME")
INFOBLOX_PASSWORD = os.getenv("INFOBLOX_PASSWORD")
TOOLS_FILE = "/app/tools.yaml"

# Load Swagger JSON
with open("/app/swagger.json", "r") as f:
    swagger_data = json.load(f)

# Fetch WAPI schema
def fetch_wapi_schema():
    response = requests.get(f"{GRID_MASTER_URL}/_schema", auth=(INFOBLOX_USERNAME, INFOBLOX_PASSWORD), verify=False)
    response.raise_for_status()
    return response.json()

# Generate tools from Swagger and schema
def generate_tools():
    schema = fetch_wapi_schema()
    tools = []
    for path, methods in swagger_data["paths"].items():
        for method, details in methods.items():
            tool = {
                "name": details.get("operationId", path.replace("/", "_").strip("_")),
                "description": details.get("summary", ""),
                "endpoint": f"{GRID_MASTER_URL}{path}",
                "method": method.upper(),
                "parameters": details.get("parameters", []),
                "schema_ref": details.get("responses", {}).get("200", {}).get("schema", {})
            }
            tools.append(tool)
    with open(TOOLS_FILE, "w") as f:
        yaml.dump(tools, f)
    return tools

# Load tools
if not os.path.exists(TOOLS_FILE):
    generate_tools()
with open(TOOLS_FILE, "r") as f:
    TOOLS = yaml.load(f, Loader=yaml.SafeLoader)

class ToolRequest(BaseModel):
    parameters: dict

@app.get("/tools")
async def list_tools():
    return TOOLS

@app.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, request: ToolRequest):
    tool = next((t for t in TOOLS if t["name"] == tool_name), None)
    if not tool:
        raise HTTPException(status_code=404, detail="Tool not found")
    try:
        headers = {"Content-Type": "application/json"}
        response = requests.request(
            method=tool["method"],
            url=tool["endpoint"],
            auth=(INFOBLOX_USERNAME, INFOBLOX_PASSWORD),
            json=request.parameters,
            headers=headers,
            verify=False
        )
        response.raise_for_status()
        return response.json()
    except requests.RequestException as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("MCP_PORT", 8000)))
EOF

# Create MCP Dockerfile
print_info "Creating MCP Dockerfile..."
cat > Dockerfile.mcp << 'EOF'
FROM python:3.8-slim
WORKDIR /app
COPY mcp_server.py /app/
COPY swagger.json /app/
RUN pip install requests fastapi uvicorn pyyaml
EXPOSE 8000
CMD ["uvicorn", "mcp_server:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# Create Open WebUI pipeline
print_info "Creating Open WebUI pipeline..."
cat > "open-webui/pipelines/mcp_pipeline.py" << 'EOF'
import requests
from typing import Dict, Any
import os

MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://mcp:8000")

def mcp_pipeline(prompt: str, rag_results: Dict[str, Any]) -> Dict[str, Any]:
    tools_response = requests.get(f"{MCP_SERVER_URL}/tools").json()
    if "network utilization" in prompt.lower():
        tool_name = "get_network"
        params = {"network": prompt.split("for")[-1].strip()}
        response = requests.post(f"{MCP_SERVER_URL}/tools/{tool_name}", json={"parameters": params}).json()
        return {"result": response, "tool": tool_name}
    else:
        return {"tools": tools_response, "message": "Please select a tool"}
EOF

# Create supervisord configuration for single container
print_info "Creating supervisord configuration..."
cat > supervisord.conf << 'EOF'
[supervisord]
nodaemon=true

[program:open-webui]
command=/app/backend/start.sh
directory=/app
environment=OPEN_WEBUI_PORT=3000,MCP_SERVER_URL=http://localhost:8000,XAI_API_KEY=%(ENV_XAI_API_KEY)s,ANTHROPIC_API_KEY=%(ENV_ANTHROPIC_API_KEY)s
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:mcp]
command=uvicorn mcp_server:app --host 0.0.0.0 --port 8000
directory=/app
environment=GRID_MASTER_URL=%(ENV_GRID_MASTER_URL)s,INFOBLOX_USERNAME=%(ENV_INFOBLOX_USERNAME)s,INFOBLOX_PASSWORD=%(ENV_INFOBLOX_PASSWORD)s,MCP_PORT=8000
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
EOF

# Create Dockerfile for single container
print_info "Creating single container Dockerfile..."
cat > Dockerfile.single << 'EOF'
FROM ghcr.io/open-webui/open-webui:main
WORKDIR /app
RUN apt-get update && apt-get install -y python3-pip supervisor && pip3 install requests fastapi uvicorn pyyaml
COPY mcp_server.py /app/
COPY swagger.json /app/
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY open-webui/pipelines /app/backend/pipelines
COPY open-webui/swagger.json /app/backend/data/swagger.json
EXPOSE 3000 8000
CMD ["/usr/bin/supervisord"]
EOF

# Create Docker Compose file based on user choice
print_info "Creating Docker Compose configuration..."

if [ "$DOCKER_CHOICE" = "1" ]; then
    # Single container setup
    cat > docker-compose.yml << EOF
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.single
    ports:
      - "$OPEN_WEBUI_PORT:3000"
    environment:
      - GRID_MASTER_URL=$GRID_MASTER_URL
      - INFOBLOX_USERNAME=$INFOBLOX_USERNAME
      - INFOBLOX_PASSWORD=$INFOBLOX_PASSWORD
      - XAI_API_KEY=$XAI_API_KEY
      - ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY
      - RAG_EMBEDDING_MODEL=nomic-embed-text
      - LLM_MODELS=grok,claude
    volumes:
      - ./open-webui/config:/app/backend/data
      - ./tools.yaml:/app/tools.yaml
    networks:
      - infoblox-net
networks:
  infoblox-net:
    driver: bridge
EOF
else
    # Two container setup
    cat > docker-compose.yml << EOF
version: '3.8'
services:
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    ports:
      - "$OPEN_WEBUI_PORT:3000"
    environment:
      - MCP_SERVER_URL=http://mcp:8000
      - XAI_API_KEY=$XAI_API_KEY
      - ANTHROPIC_API_KEY=$ANTHROPIC_API_KEY
      - RAG_EMBEDDING_MODEL=nomic-embed-text
      - LLM_MODELS=grok,claude
    volumes:
      - ./open-webui/config:/app/backend/data
      - ./open-webui/pipelines:/app/backend/pipelines
      - ./open-webui/swagger.json:/app/backend/data/swagger.json
    networks:
      - infoblox-net
    depends_on:
      - mcp
  mcp:
    build:
      context: .
      dockerfile: Dockerfile.mcp
    ports:
      - "$MCP_PORT:8000"
    environment:
      - GRID_MASTER_URL=$GRID_MASTER_URL
      - INFOBLOX_USERNAME=$INFOBLOX_USERNAME
      - INFOBLOX_PASSWORD=$INFOBLOX_PASSWORD
    volumes:
      - ./swagger.json:/app/swagger.json
      - ./tools.yaml:/app/tools.yaml
    networks:
      - infoblox-net
networks:
  infoblox-net:
    driver: bridge
EOF
fi

print_success "All configuration files created successfully!"

# Prompt to start the app
echo
echo -n "Setup complete. Do you want to start the application now? (y/n): "
read START_APP

if [[ "$START_APP" =~ ^[Yy]$ ]]; then
    print_info "Starting services with Docker Compose..."
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d --build
    else
        docker compose up -d --build
    fi
    print_success "Services started successfully!"
else
    print_info "Skipping application start."
fi

# Provide start/stop instructions
echo
print_success "Setup Summary:"
echo "Open WebUI: http://localhost:$OPEN_WEBUI_PORT"
if [ "$DOCKER_CHOICE" = "2" ]; then
    echo "MCP Server: $MCP_SERVER_URL"
fi
echo
print_info "To start the services later, run:"
echo "  cd $PROJECT_DIR"
if command -v docker-compose &> /dev/null; then
    echo "  docker-compose up -d"
else
    echo "  docker compose up -d"
fi
echo
print_info "To stop the services, run:"
echo "  cd $PROJECT_DIR"
if command -v docker-compose &> /dev/null; then
    echo "  docker-compose down"
else
    echo "  docker compose down"
fi
echo
print_info "To view logs, run:"
if command -v docker-compose &> /dev/null; then
    echo "  docker-compose logs"
else
    echo "  docker compose logs"
fi
echo
print_info "Next steps:"
echo "1. Access Open WebUI at http://localhost:$OPEN_WEBUI_PORT"
echo "2. Upload Swagger/WAPI docs to RAG"
echo "3. Test MCP integration"
echo
print_success "Installation complete! Enjoy using Open WebUI with InfoBlox MCP integration."

# setup.ps1: Set up Open WebUI with InfoBlox MCP and LLMs using Docker Compose
# Usage: .\setup.ps1

# Stop on error
$ErrorActionPreference = "Stop"

# Default values
$ProjectDir = "$env:USERPROFILE\infoblox-open-webui"
$DefaultOpenWebUIPort = 3000
$DefaultMcpPort = 8000
$SwaggerUrl = "https://raw.githubusercontent.com/infobloxopen/infoblox-swagger-wapi/master/swagger.json"
$DefaultGridMasterUrl = "https://grid-master/wapi/v2.13.1"
$DefaultMcpServerUrl = "http://localhost"

# Function to check if a port is free and find an alternative
function Check-Port {
param (
[int]$Port
)
$maxAttempts = 10
$attempt = 0
while ($attempt -lt $maxAttempts) {
if (-not (Get-NetTCPConnection -LocalPort $Port -ErrorAction SilentlyContinue)) {
return $Port
}
$Port++
$attempt++
}
Write-Error "Could not find a free port after $maxAttempts attempts"
exit 1
}

# Prompt for user inputs
$GridMasterUrl = Read-Host -Prompt "Enter InfoBlox Grid Master URL (default: $DefaultGridMasterUrl)"
if (-not $GridMasterUrl) { $GridMasterUrl = $DefaultGridMasterUrl }

$InfobloxUsername = Read-Host -Prompt "Enter InfoBlox username (default: admin)"
if (-not $InfobloxUsername) { $InfobloxUsername = "admin" }

$InfobloxPassword = Read-Host -Prompt "Enter InfoBlox password (default: infoblox)" -AsSecureString
if (-not $InfobloxPassword) {
$InfobloxPassword = "infoblox"
} else {
$InfobloxPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($InfobloxPassword))
}

$XaiApiKey = Read-Host -Prompt "Enter xAI API key for Grok (leave blank if not using)" -AsSecureString
if ($XaiApiKey) {
$XaiApiKey = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($XaiApiKey))
}

$AnthropicApiKey = Read-Host -Prompt "Enter Anthropic API key for Claude (leave blank if not using)" -AsSecureString
if ($AnthropicApiKey) {
$AnthropicApiKey = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AnthropicApiKey))
}

# Check and assign ports
$OpenWebUIPort = Check-Port $DefaultOpenWebUIPort
$McpPort = Check-Port $DefaultMcpPort
$McpServerUrl = "$DefaultMcpServerUrl:$McpPort"

Write-Host "Using Open WebUI port: $OpenWebUIPort"
Write-Host "Using MCP port: $McpPort"

Write-Host "Choose Docker setup:"
Write-Host "1) Single container (Open WebUI + MCP)"
Write-Host "2) Two containers (Open WebUI, MCP separate)"
$DockerChoice = Read-Host -Prompt "Enter 1 or 2"
if ($DockerChoice -notin @("1", "2")) {
Write-Error "Invalid choice. Exiting."
exit 1
}

# Validate Docker and Docker Compose
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
Write-Error "Docker is required. Please install Docker Desktop and try again."
exit 1
}
if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
Write-Error "Docker Compose is required. Please install Docker Compose and try again."
exit 1
}

# Create project directory
Write-Host "Creating project directory at $ProjectDir"
New-Item -ItemType Directory -Force -Path $ProjectDir | Out-Null
Set-Location $ProjectDir

# Create directories for Open WebUI and MCP
New-Item -ItemType Directory -Force -Path "open-webui\config" | Out-Null
New-Item -ItemType Directory -Force -Path "open-webui\pipelines" | Out-Null

# Download Swagger JSON
Write-Host "Downloading Swagger JSON from $SwaggerUrl"
Invoke-WebRequest -Uri $SwaggerUrl -OutFile "open-webui\swagger.json"
Copy-Item -Path "open-webui\swagger.json" -Destination "swagger.json"

# Create MCP server script
@'
import requests
import yaml
import json
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import os

app = FastAPI()

# Configuration
GRID_MASTER_URL = os.getenv("GRID_MASTER_URL")
INFOBLOX_USERNAME = os.getenv("INFOBLOX_USERNAME")
INFOBLOX_PASSWORD = os.getenv("INFOBLOX_PASSWORD")
TOOLS_FILE = "/app/tools.yaml"

# Load Swagger JSON
with open("/app/swagger.json", "r") as f:
swagger_data = json.load(f)

# Fetch WAPI schema
def fetch_wapi_schema():
response = requests.get(f"{GRID_MASTER_URL}/_schema", auth=(INFOBLOX_USERNAME, INFOBLOX_PASSWORD), verify=False)
response.raise_for_status()
return response.json()

# Generate tools from Swagger and schema
def generate_tools():
schema = fetch_wapi_schema()
tools = []
for path, methods in swagger_data["paths"].items():
for method, details in methods.items():
tool = {
"name": details.get("operationId", path.replace("/", "_").strip("_")),
"description": details.get("summary", ""),
"endpoint": f"{GRID_MASTER_URL}{path}",
"method": method.upper(),
"parameters": details.get("parameters", []),
"schema_ref": details.get("responses", {}).get("200", {}).get("schema", {})
}
tools.append(tool)
with open(TOOLS_FILE, "w") as f:
yaml.dump(tools, f)
return tools

# Load tools
if not os.path.exists(TOOLS_FILE):
generate_tools()
with open(TOOLS_FILE, "r") as f:
TOOLS = yaml.load(f, Loader=yaml.SafeLoader)

class ToolRequest(BaseModel):
parameters: dict

@app.get("/tools")
async def list_tools():
return TOOLS

@app.post("/tools/{tool_name}")
async def execute_tool(tool_name: str, request: ToolRequest):
tool = next((t for t in TOOLS if t["name"] == tool_name), None)
if not tool:
raise HTTPException(status_code=404, detail="Tool not found")
try:
headers = {"Content-Type": "application/json"}
response = requests.request(
method=tool["method"],
url=tool["endpoint"],
auth=(INFOBLOX_USERNAME, INFOBLOX_PASSWORD),
json=request.parameters,
headers=headers,
verify=False
)
response.raise_for_status()
return response.json()
except requests.RequestException as e:
raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
import uvicorn
uvicorn.run(app, host="0.0.0.0", port=int(os.getenv("MCP_PORT", 8000)))
'@ | Out-File -FilePath mcp_server.py -Encoding utf8

# Create MCP Dockerfile
@'
FROM python:3.8-slim
WORKDIR /app
COPY mcp_server.py /app/
COPY swagger.json /app/
RUN pip install requests fastapi uvicorn pyyaml
EXPOSE 8000
CMD ["uvicorn", "mcp_server:app", "--host", "0.0.0.0", "--port", "8000"]
'@ | Out-File -FilePath Dockerfile.mcp -Encoding utf8

# Create Open WebUI pipeline
@'
import requests
from typing import Dict, Any
import os

MCP_SERVER_URL = os.getenv("MCP_SERVER_URL", "http://mcp:8000")

def mcp_pipeline(prompt: str, rag_results: Dict[str, Any]) -> Dict[str, Any]:
tools_response = requests.get(f"{MCP_SERVER_URL}/tools").json()
if "network utilization" in prompt.lower():
tool_name = "get_network"
params = {"network": prompt.split("for")[-1].strip()}
response = requests.post(f"{MCP_SERVER_URL}/tools/{tool_name}", json={"parameters": params}).json()
return {"result": response, "tool": tool_name}
else:
return {"tools": tools_response, "message": "Please select a tool"}
'@ | Out-File -FilePath "open-webui\pipelines\mcp_pipeline.py" -Encoding utf8

# Create supervisord configuration for single container
@'
[supervisord]
nodaemon=true

[program:open-webui]
command=/app/backend/start.sh
directory=/app
environment=OPEN_WEBUI_PORT=3000,MCP_SERVER_URL=http://localhost:8000,XAI_API_KEY=%(ENV_XAI_API_KEY)s,ANTHROPIC_API_KEY=%(ENV_ANTHROPIC_API_KEY)s
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:mcp]
command=uvicorn mcp_server:app --host 0.0.0.0 --port 8000
directory=/app
environment=GRID_MASTER_URL=%(ENV_GRID_MASTER_URL)s,INFOBLOX_USERNAME=%(ENV_INFOBLOX_USERNAME)s,INFOBLOX_PASSWORD=%(ENV_INFOBLOX_PASSWORD)s,MCP_PORT=8000
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
'@ | Out-File -FilePath supervisord.conf -Encoding utf8

# Create Dockerfile for single container
@'
FROM ghcr.io/open-webui/open-webui:main
WORKDIR /app
RUN apt-get update && apt-get install -y python3-pip supervisor && pip3 install requests fastapi uvicorn pyyaml
COPY mcp_server.py /app/
COPY swagger.json /app/
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY open-webui/pipelines /app/backend/pipelines
COPY open-webui/swagger.json /app/backend/data/swagger.json
EXPOSE 3000 8000
CMD ["/usr/bin/supervisord"]
'@ | Out-File -FilePath Dockerfile.single -Encoding utf8

# Create Docker Compose file based on user choice
if ($DockerChoice -eq "1") {
@"
version: '3.8'
services:
app:
build:
context: .
dockerfile: Dockerfile.single
ports:
- "$OpenWebUIPort:3000"
environment:
- GRID_MASTER_URL=$GridMasterUrl
- INFOBLOX_USERNAME=$InfobloxUsername
- INFOBLOX_PASSWORD=$InfobloxPassword
- XAI_API_KEY=$XaiApiKey
- ANTHROPIC_API_KEY=$AnthropicApiKey
- RAG_EMBEDDING_MODEL=nomic-embed-text
- LLM_MODELS=grok,claude
volumes:
- ./open-webui/config:/app/backend/data
- ./tools.yaml:/app/tools.yaml
networks:
- infoblox-net
networks:
infoblox-net:
driver: bridge
"@ | Out-File -FilePath docker-compose.yml -Encoding utf8
}
else {
@"
version: '3.8'
services:
open-webui:
image: ghcr.io/open-webui/open-webui:main
ports:
- "$OpenWebUIPort:3000"
environment:
- MCP_SERVER_URL=http://mcp:8000
- XAI_API_KEY=$XaiApiKey
- ANTHROPIC_API_KEY=$AnthropicApiKey
- RAG_EMBEDDING_MODEL=nomic-embed-text
- LLM_MODELS=grok,claude
volumes:
- ./open-webui/config:/app/backend/data
- ./open-webui/pipelines:/app/backend/pipelines
- ./open-webui/swagger.json:/app/backend/data/swagger.json
networks:
- infoblox-net
depends_on:
- mcp
mcp:
build:
context: .
dockerfile: Dockerfile.mcp
ports:
- "$McpPort:8000"
environment:
- GRID_MASTER_URL=$GridMasterUrl
- INFOBLOX_USERNAME=$InfobloxUsername
- INFOBLOX_PASSWORD=$InfobloxPassword
volumes:
- ./swagger.json:/app/swagger.json
- ./tools.yaml:/app/tools.yaml
networks:
- infoblox-net
networks:
infoblox-net:
driver: bridge
"@ | Out-File -FilePath docker-compose.yml -Encoding utf8
}

# Prompt to start the app
$startApp = Read-Host -Prompt "Setup complete. Do you want to start the application now? (y/n)"
if ($startApp -match '^[Yy]$') {
Write-Host "Starting services with Docker Compose..."
docker-compose up -d --build
}
else {
Write-Host "Skipping application start."
}

# Provide start/stop instructions
Write-Host
Write-Host "Setup Summary:"
Write-Host "Open WebUI: http://localhost:$OpenWebUIPort"
if ($DockerChoice -eq "2") {
Write-Host "MCP Server: $McpServerUrl"
}
Write-Host
Write-Host "To start the services later, run:"
Write-Host " cd $ProjectDir"
Write-Host " docker-compose up -d"
Write-Host
Write-Host "To stop the services, run:"
Write-Host " cd $ProjectDir"
Write-Host " docker-compose down"
Write-Host
Write-Host "To view logs, run:"
Write-Host " docker-compose logs"
Write-Host
Write-Host "Next steps: Access Open WebUI at http://localhost:$OpenWebUIPort, upload Swagger/WAPI docs to RAG, and test MCP integration."